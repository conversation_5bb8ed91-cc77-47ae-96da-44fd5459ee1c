<template>
  <div id="data-view">
    <dv-full-screen-container>
      <!-- 上部区域：标题 + 数据概览 -->
      <div class="top-section">
        <top-header />
        <digital-flop />
      </div>

      <!-- 中部区域：主要数据展示 -->
      <div class="middle-section">
        <div class="middle-left">
          <college-table />
        </div>
        <div class="middle-center">
          <rose-chart />
        </div>
        <div class="middle-right">
          <scroll-board />
        </div>
      </div>

      <!-- 下部区域：学院表格和其他组件 -->
      <div class="bottom-section">
        <div class="bottom-left">
          <!-- <college-table /> -->
          <!-- <ranking-board /> -->
        </div>
        <div class="bottom-right">
          <scroll-board />
        </div>
      </div>
    </dv-full-screen-container>
  </div>
</template>

<script>
import topHeader from './topHeader'
import digitalFlop from './digitalFlop'
import rankingBoard from './rankingBoard'
import roseChart from './roseChart'
import waterLevelChart from './waterLevelChart'
import scrollBoard from './scrollBoard'
import cards from './cards'
import collegeTable from './collegeTable'

export default {
  name: 'DataView',
  components: {
    topHeader,
    digitalFlop,
    rankingBoard,
    roseChart,
    waterLevelChart,
    scrollBoard,
    cards,
    collegeTable
  },
  data () {
    return {}
  },
  methods: {}
}
</script>

<style lang="less">
#data-view {
  width: 100%;
  height: 100%;
  background-color: #030409;
  color: #fff;

  #dv-full-screen-container {
    background-image: url('./img/bg.png');
    background-size: 100% 100%;
    box-shadow: 0 0 3px blue;
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding: 10px;
    box-sizing: border-box;
  }

  // 上部区域：标题 + 数据概览 (25%)
  .top-section {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
  }

  // 中部区域：主要数据展示 (55%)
  .middle-section {
    height: 30%;
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
  }

  .middle-left {
    width: 30%;
    display: flex;
    flex-direction: column;
  }

  .middle-center {
    width: 40%;
    display: flex;
    flex-direction: column;
  }

  .middle-right {
    width: 30%;
    display: flex;
    flex-direction: column;
  }

  // 下部区域：学院表格和其他组件 (20%)
  .bottom-section {
    height: 20%;
    display: flex;
    gap: 15px;
  }

  .bottom-left {
    width: 60%;
    display: flex;
    flex-direction: column;
  }

  .bottom-right {
    width: 40%;
    display: flex;
    flex-direction: column;
  }
}
</style>
